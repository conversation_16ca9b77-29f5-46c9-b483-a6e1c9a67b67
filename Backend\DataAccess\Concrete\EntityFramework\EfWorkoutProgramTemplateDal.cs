using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace DataAccess.Concrete.EntityFramework
{
    public class EfWorkoutProgramTemplateDal : EfCompanyEntityRepositoryBase<WorkoutProgramTemplate, GymContext>, IWorkoutProgramTemplateDal
    {
        private readonly ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfWorkoutProgramTemplateDal(ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }










        /// <summary>
        /// SOLID prensiplerine uygun: Validation logic DAL katmanında
        /// </summary>


        /// <summary>
        /// SOLID prensiplerine uygun: Business rules validation ve add işlemi DAL katmanında
        /// </summary>


        /// <summary>
        /// SOLID prensiplerine uygun: Business rules validation ve update işlemi DAL katmanında
        /// </summary>


        /// <summary>
        /// SOLID prensiplerine uygun: Soft delete validation DAL katmanında
        /// </summary>


        /// <summary>
        /// SOLID prensiplerine uygun: Business rules validation helper method
        /// </summary>
        private IResult ValidateWorkoutProgramBusinessRules(string programName, List<WorkoutProgramDayAddDto> days, int? excludeId = null)
        {
            // Program adı kontrolü
            bool nameExists = CheckProgramNameExistsInternal(programName, excludeId);
            if (nameExists)
            {
                return new ErrorResult("Bu program adı zaten kullanılıyor.");
            }

            // Maksimum gün sayısı kontrolü
            const int MAX_DAYS = 7;
            if (days.Count > MAX_DAYS)
            {
                return new ErrorResult($"Bir antrenman programı en fazla {MAX_DAYS} gün olabilir.");
            }

            // Gün numaralarının kontrolü
            var dayNumbers = days.Select(d => d.DayNumber).ToList();
            if (dayNumbers.Distinct().Count() != dayNumbers.Count)
            {
                return new ErrorResult("Aynı gün numarası birden fazla kez kullanılamaz.");
            }

            if (dayNumbers.Any(d => d < 1 || d > 7))
            {
                return new ErrorResult("Gün numaraları 1-7 arasında olmalıdır.");
            }

            // En az bir antrenman günü kontrolü
            if (!days.Any(d => !d.IsRestDay))
            {
                return new ErrorResult("En az bir antrenman günü olmalıdır.");
            }

            return new SuccessResult();
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Business rules validation helper method for update
        /// </summary>
        private IResult ValidateWorkoutProgramBusinessRules(string programName, List<WorkoutProgramDayUpdateDto> days, int? excludeId = null)
        {
            // Program adı kontrolü
            bool nameExists = CheckProgramNameExistsInternal(programName, excludeId);
            if (nameExists)
            {
                return new ErrorResult("Bu program adı zaten kullanılıyor.");
            }

            // Maksimum gün sayısı kontrolü
            const int MAX_DAYS = 7;

            if (days.Count > MAX_DAYS)
            {
                return new ErrorResult($"Bir antrenman programı en fazla {MAX_DAYS} gün olabilir.");
            }

            // Gün numaralarının kontrolü
            var dayNumbers = days.Select(d => d.DayNumber).ToList();
            if (dayNumbers.Distinct().Count() != dayNumbers.Count)
            {
                return new ErrorResult("Aynı gün numarası birden fazla kez kullanılamaz.");
            }

            if (dayNumbers.Any(d => d < 1 || d > 7))
            {
                return new ErrorResult("Gün numaraları 1-7 arasında olmalıdır.");
            }

            // En az bir antrenman günü kontrolü
            if (!days.Any(d => !d.IsRestDay))
            {
                return new ErrorResult("En az bir antrenman günü olmalıdır.");
            }


	            return new SuccessResult();
	        }



        private bool CheckProgramNameExistsInternal(string programName, int? excludeId = null)
        {
            int companyId = _companyContext.GetCompanyId();
            var query = _context.WorkoutProgramTemplates.AsNoTracking()
                .Where(t => t.ProgramName == programName && t.IsActive == true && t.CompanyID == companyId);
            if (excludeId.HasValue)
            {
                query = query.Where(t => t.WorkoutProgramTemplateID != excludeId.Value);
            }
            return query.Any();
        }

        // Async versiyonlar
        public async Task<List<WorkoutProgramTemplateListDto>> GetWorkoutProgramTemplateListAsync(CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();

            // N+1 FIX: DayCount ve ExerciseCount için subquery'ler hazırla
            var dayCountSubquery = from wpd in _context.WorkoutProgramDays.AsNoTracking()
                                   group wpd by wpd.WorkoutProgramTemplateID into g
                                   select new { WorkoutProgramTemplateID = g.Key, DayCount = g.Count() };

            var exerciseCountSubquery = from wpd in _context.WorkoutProgramDays.AsNoTracking()
                                        join wpe in _context.WorkoutProgramExercises.AsNoTracking() on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                        group wpe by wpd.WorkoutProgramTemplateID into g
                                        select new { WorkoutProgramTemplateID = g.Key, ExerciseCount = g.Count() };

            var query = from wpt in _context.WorkoutProgramTemplates.AsNoTracking()
                        // N+1 FIX: Subquery'leri join et
                        join dayCount in dayCountSubquery on wpt.WorkoutProgramTemplateID equals dayCount.WorkoutProgramTemplateID into dayGroup
                        from dayCount in dayGroup.DefaultIfEmpty()
                        join exerciseCount in exerciseCountSubquery on wpt.WorkoutProgramTemplateID equals exerciseCount.WorkoutProgramTemplateID into exerciseGroup
                        from exerciseCount in exerciseGroup.DefaultIfEmpty()
                        where wpt.CompanyID == companyId && wpt.IsActive == true
                        select new WorkoutProgramTemplateListDto
                        {
                            WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                            ProgramName = wpt.ProgramName,
                            Description = wpt.Description,
                            ExperienceLevel = wpt.ExperienceLevel,
                            TargetGoal = wpt.TargetGoal,
                            IsActive = wpt.IsActive,
                            CreationDate = wpt.CreationDate,
                            // N+1 FIX: Artık tek sorguda geliyor!
                            DayCount = dayCount != null ? dayCount.DayCount : 0,
                            ExerciseCount = exerciseCount != null ? exerciseCount.ExerciseCount : 0
                        };
            return await query.OrderByDescending(x => x.CreationDate).ToListAsync(ct);
        }

        public async Task<WorkoutProgramTemplateDto> GetWorkoutProgramTemplateDetailAsync(int templateId, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();

            // N+1 FIX: DayCount için subquery hazırla
            var dayCountSubquery = from wpd in _context.WorkoutProgramDays.AsNoTracking()
                                   where wpd.WorkoutProgramTemplateID == templateId
                                   group wpd by wpd.WorkoutProgramTemplateID into g
                                   select new { WorkoutProgramTemplateID = g.Key, DayCount = g.Count() };

            var template = await (from wpt in _context.WorkoutProgramTemplates.AsNoTracking()
                                  // N+1 FIX: Subquery'yi join et
                                  join dayCount in dayCountSubquery on wpt.WorkoutProgramTemplateID equals dayCount.WorkoutProgramTemplateID into dayGroup
                                  from dayCount in dayGroup.DefaultIfEmpty()
                                  where wpt.WorkoutProgramTemplateID == templateId && wpt.CompanyID == companyId
                                  select new WorkoutProgramTemplateDto
                                  {
                                      WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                                      CompanyID = wpt.CompanyID,
                                      ProgramName = wpt.ProgramName,
                                      Description = wpt.Description,
                                      ExperienceLevel = wpt.ExperienceLevel,
                                      TargetGoal = wpt.TargetGoal,
                                      IsActive = wpt.IsActive,
                                      CreationDate = wpt.CreationDate,
                                      // N+1 FIX: Artık tek sorguda geliyor!
                                      DayCount = dayCount != null ? dayCount.DayCount : 0
                                  }).FirstOrDefaultAsync(ct);

            if (template != null)
            {
                template.Days = await GetWorkoutProgramDaysAsync(_context, templateId, ct);
            }
            return template;
        }

        private async Task<List<WorkoutProgramDayDto>> GetWorkoutProgramDaysAsync(GymContext context, int templateId, CancellationToken ct)
        {
            var days = await context.WorkoutProgramDays.AsNoTracking()
                .Where(d => d.WorkoutProgramTemplateID == templateId)
                .Select(d => new WorkoutProgramDayDto
                {
                    WorkoutProgramDayID = d.WorkoutProgramDayID,
                    WorkoutProgramTemplateID = d.WorkoutProgramTemplateID,
                    DayNumber = d.DayNumber,
                    DayName = d.DayName,
                    IsRestDay = d.IsRestDay,
                    CreationDate = d.CreationDate
                })
                .OrderBy(d => d.DayNumber)
                .ToListAsync(ct);

            if (days.Count == 0)
            {
                return days;
            }

            var dayIds = days.Select(d => d.WorkoutProgramDayID).ToList();

            var exercises = await (from wpe in context.WorkoutProgramExercises.AsNoTracking()
                                   where dayIds.Contains(wpe.WorkoutProgramDayID)
                                   orderby wpe.WorkoutProgramDayID, wpe.OrderIndex
                                   select new WorkoutProgramExerciseDto
                                   {
                                       WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                                       WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                                       ExerciseType = wpe.ExerciseType,
                                       ExerciseID = wpe.ExerciseID,
                                       OrderIndex = wpe.OrderIndex,
                                       Sets = wpe.Sets,
                                       Reps = wpe.Reps,
                                       RestTime = wpe.RestTime,
                                       Notes = wpe.Notes,
                                       CreationDate = wpe.CreationDate
                                   }).ToListAsync(ct);

            var systemIds = exercises.Where(e => e.ExerciseType == "System").Select(e => e.ExerciseID).Distinct().ToList();
            var companyIds = exercises.Where(e => e.ExerciseType == "Company").Select(e => e.ExerciseID).Distinct().ToList();

            var systemMeta = systemIds.Count > 0
                ? await (from e in context.SystemExercises.AsNoTracking()
                         join ec in context.ExerciseCategories.AsNoTracking() on e.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where systemIds.Contains(e.SystemExerciseID)
                         select new { e.SystemExerciseID, e.ExerciseName, e.Description, ec.CategoryName })
                        .ToDictionaryAsync(x => x.SystemExerciseID, x => (x.ExerciseName, x.Description, x.CategoryName), ct)
                : new Dictionary<int, (string ExerciseName, string Description, string CategoryName)>();

            var companyMeta = companyIds.Count > 0
                ? await (from ce in context.CompanyExercises.AsNoTracking()
                         join ec in context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where companyIds.Contains(ce.CompanyExerciseID)
                         select new { ce.CompanyExerciseID, ce.ExerciseName, ce.Description, ec.CategoryName })
                        .ToDictionaryAsync(x => x.CompanyExerciseID, x => (x.ExerciseName, x.Description, x.CategoryName), ct)
                : new Dictionary<int, (string ExerciseName, string Description, string CategoryName)>();

            foreach (var ex in exercises)
            {
                if (ex.ExerciseType == "System" && systemMeta.TryGetValue(ex.ExerciseID, out var sm))
                {
                    ex.ExerciseName = sm.ExerciseName;
                    ex.ExerciseDescription = sm.Description;
                    ex.CategoryName = sm.CategoryName;
                }
                else if (ex.ExerciseType == "Company" && companyMeta.TryGetValue(ex.ExerciseID, out var cm))
                {
                    ex.ExerciseName = cm.ExerciseName;
                    ex.ExerciseDescription = cm.Description;
                    ex.CategoryName = cm.CategoryName;
                }
            }

            var exercisesByDay = exercises
                .GroupBy(e => e.WorkoutProgramDayID)
                .ToDictionary(g => g.Key, g => g.OrderBy(x => x.OrderIndex).ToList());

            foreach (var day in days)
            {
                if (exercisesByDay.TryGetValue(day.WorkoutProgramDayID, out var list))
                {
                    day.Exercises = list;
                }
                else
                {
                    day.Exercises = new List<WorkoutProgramExerciseDto>();
                }
            }

            return days;
        }

        private async Task<List<WorkoutProgramExerciseDto>> GetWorkoutProgramExercisesAsync(GymContext context, int dayId, CancellationToken ct)
        {
            var result = await (from wpe in context.WorkoutProgramExercises.AsNoTracking()
                                where wpe.WorkoutProgramDayID == dayId
                                orderby wpe.OrderIndex
                                select new WorkoutProgramExerciseDto
                                {
                                    WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                                    WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                                    ExerciseType = wpe.ExerciseType,
                                    ExerciseID = wpe.ExerciseID,
                                    OrderIndex = wpe.OrderIndex,
                                    Sets = wpe.Sets,
                                    Reps = wpe.Reps,
                                    RestTime = wpe.RestTime,
                                    Notes = wpe.Notes,
                                    CreationDate = wpe.CreationDate
                                }).ToListAsync(ct);

            if (result.Count == 0)
            {
                return result;
            }

            var systemIds = result.Where(e => e.ExerciseType == "System").Select(e => e.ExerciseID).Distinct().ToList();
            var companyIds = result.Where(e => e.ExerciseType == "Company").Select(e => e.ExerciseID).Distinct().ToList();

            var systemMeta = systemIds.Count > 0
                ? await (from e in context.SystemExercises.AsNoTracking()
                         join ec in context.ExerciseCategories.AsNoTracking() on e.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where systemIds.Contains(e.SystemExerciseID)
                         select new { e.SystemExerciseID, e.ExerciseName, e.Description, ec.CategoryName })
                        .ToDictionaryAsync(x => x.SystemExerciseID, x => (x.ExerciseName, x.Description, x.CategoryName), ct)
                : new Dictionary<int, (string ExerciseName, string Description, string CategoryName)>();

            var companyMeta = companyIds.Count > 0
                ? await (from ce in context.CompanyExercises.AsNoTracking()
                         join ec in context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where companyIds.Contains(ce.CompanyExerciseID)
                         select new { ce.CompanyExerciseID, ce.ExerciseName, ce.Description, ec.CategoryName })
                        .ToDictionaryAsync(x => x.CompanyExerciseID, x => (x.ExerciseName, x.Description, x.CategoryName), ct)
                : new Dictionary<int, (string ExerciseName, string Description, string CategoryName)>();

            foreach (var ex in result)
            {
                if (ex.ExerciseType == "System" && systemMeta.TryGetValue(ex.ExerciseID, out var sm))
                {
                    ex.ExerciseName = sm.ExerciseName;
                    ex.ExerciseDescription = sm.Description;
                    ex.CategoryName = sm.CategoryName;
                }
                else if (ex.ExerciseType == "Company" && companyMeta.TryGetValue(ex.ExerciseID, out var cm))
                {
                    ex.ExerciseName = cm.ExerciseName;
                    ex.ExerciseDescription = cm.Description;
                    ex.CategoryName = cm.CategoryName;
                }
            }

            return result;
        }

        public async Task<bool> CheckProgramNameExistsAsync(string programName, int? excludeId = null, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            var query = _context.WorkoutProgramTemplates.AsNoTracking()
                .Where(t => t.ProgramName == programName && t.IsActive == true && t.CompanyID == companyId);
            if (excludeId.HasValue)
            {
                query = query.Where(t => t.WorkoutProgramTemplateID != excludeId.Value);
            }
            return await query.AnyAsync(ct);
        }

        public async Task AddWorkoutProgramWithDaysAndExercisesAsync(WorkoutProgramTemplateAddDto templateAddDto, int companyId, CancellationToken ct = default)
        {
            var template = new WorkoutProgramTemplate
            {
                CompanyID = companyId,
                ProgramName = templateAddDto.ProgramName,
                Description = templateAddDto.Description,
                ExperienceLevel = templateAddDto.ExperienceLevel,
                TargetGoal = templateAddDto.TargetGoal,
                IsActive = true,
                CreationDate = System.DateTime.Now
            };

            await _context.WorkoutProgramTemplates.AddAsync(template, ct);
            await _context.SaveChangesAsync(ct); // ID almak için

            foreach (var dayDto in templateAddDto.Days)
            {
                var day = new WorkoutProgramDay
                {
                    WorkoutProgramTemplateID = template.WorkoutProgramTemplateID,
                    CompanyID = companyId,
                    DayNumber = dayDto.DayNumber,
                    DayName = dayDto.DayName,
                    IsRestDay = dayDto.IsRestDay,
                    CreationDate = System.DateTime.Now
                };

                await _context.WorkoutProgramDays.AddAsync(day, ct);
                await _context.SaveChangesAsync(ct);

                if (!dayDto.IsRestDay)
                {
                    foreach (var exerciseDto in dayDto.Exercises)
                    {
                        var exercise = new WorkoutProgramExercise
                        {
                            WorkoutProgramDayID = day.WorkoutProgramDayID,
                            CompanyID = companyId,
                            ExerciseType = exerciseDto.ExerciseType,
                            ExerciseID = exerciseDto.ExerciseID,
                            OrderIndex = exerciseDto.OrderIndex,
                            Sets = exerciseDto.Sets,
                            Reps = exerciseDto.Reps,
                            RestTime = exerciseDto.RestTime,
                            Notes = exerciseDto.Notes,
                            CreationDate = System.DateTime.Now
                        };
                        await _context.WorkoutProgramExercises.AddAsync(exercise, ct);
                    }
                }
            }

            await _context.SaveChangesAsync(ct);
        }

        public async Task UpdateWorkoutProgramWithDaysAndExercisesAsync(WorkoutProgramTemplateUpdateDto templateUpdateDto, int companyId, CancellationToken ct = default)
        {
            var templateId = templateUpdateDto.WorkoutProgramTemplateID;
            var now = DateTime.Now;

            // LINQ/EF ile güncelleme ve silme (SQL interpolated yerine)
            var template = await _context.WorkoutProgramTemplates
                .FirstOrDefaultAsync(t => t.WorkoutProgramTemplateID == templateId && t.CompanyID == companyId, ct);
            if (template != null)
            {
                template.ProgramName = templateUpdateDto.ProgramName;
                template.Description = templateUpdateDto.Description;
                template.ExperienceLevel = templateUpdateDto.ExperienceLevel;
                template.TargetGoal = templateUpdateDto.TargetGoal;
                template.IsActive = templateUpdateDto.IsActive;
                template.UpdatedDate = now;
                _context.WorkoutProgramTemplates.Update(template);
                await _context.SaveChangesAsync(ct);
            }

            // İlgili günlerin tüm egzersizlerini sil
            var dayIdsToDelete = await _context.WorkoutProgramDays
                .Where(wpd => wpd.WorkoutProgramTemplateID == templateId && wpd.CompanyID == companyId)
                .Select(wpd => wpd.WorkoutProgramDayID)
                .ToListAsync(ct);

            if (dayIdsToDelete.Count > 0)
            {
                var exercisesToDelete = await _context.WorkoutProgramExercises
                    .Where(wpe => dayIdsToDelete.Contains(wpe.WorkoutProgramDayID) && wpe.CompanyID == companyId)
                    .ToListAsync(ct);
                if (exercisesToDelete.Count > 0)
                {
                    _context.WorkoutProgramExercises.RemoveRange(exercisesToDelete);
                    await _context.SaveChangesAsync(ct);
                }

                // Günleri sil
                var daysToDelete = await _context.WorkoutProgramDays
                    .Where(wpd => dayIdsToDelete.Contains(wpd.WorkoutProgramDayID) && wpd.CompanyID == companyId)
                    .ToListAsync(ct);
                if (daysToDelete.Count > 0)
                {
                    _context.WorkoutProgramDays.RemoveRange(daysToDelete);
                    await _context.SaveChangesAsync(ct);
                }
            }

            if (templateUpdateDto.Days?.Count > 0)
            {
                // Güvenli: EF entity ekleme ile parametreli INSERT
                var dayEntities = new List<WorkoutProgramDay>();
                foreach (var day in templateUpdateDto.Days)
                {
                    dayEntities.Add(new WorkoutProgramDay
                    {
                        WorkoutProgramTemplateID = templateId,
                        CompanyID = companyId,
                        DayNumber = day.DayNumber,
                        DayName = day.DayName,
                        IsRestDay = day.IsRestDay,
                        CreationDate = now
                    });
                }

                await _context.WorkoutProgramDays.AddRangeAsync(dayEntities, ct);
                await _context.SaveChangesAsync(ct);

                // Gün numarasından eklenen DayID'ye eşleme
                var dayIdByNumber = dayEntities.ToDictionary(d => d.DayNumber, d => d.WorkoutProgramDayID);

                // Egzersizleri parametreli şekilde ekle
                var exerciseEntities = new List<WorkoutProgramExercise>();
                foreach (var dayDto in templateUpdateDto.Days)
                {
                    if (!dayDto.IsRestDay && dayDto.Exercises?.Count > 0)
                    {
                        var dayId = dayIdByNumber[dayDto.DayNumber];
                        foreach (var exercise in dayDto.Exercises)
                        {
                            exerciseEntities.Add(new WorkoutProgramExercise
                            {
                                WorkoutProgramDayID = dayId,
                                CompanyID = companyId,
                                ExerciseType = exercise.ExerciseType,
                                ExerciseID = exercise.ExerciseID,
                                OrderIndex = exercise.OrderIndex,
                                Sets = exercise.Sets,
                                Reps = exercise.Reps,
                                RestTime = exercise.RestTime ?? 0,
                                Notes = exercise.Notes,
                                CreationDate = now
                            });
                        }
                    }
                }

                if (exerciseEntities.Count > 0)
                {
                    await _context.WorkoutProgramExercises.AddRangeAsync(exerciseEntities, ct);
                    await _context.SaveChangesAsync(ct);
                }
            }
        }

        public async Task<IDataResult<WorkoutProgramTemplate>> GetWorkoutProgramTemplateByIdWithValidationAsync(int templateId, CancellationToken ct = default)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                var template = await _context.WorkoutProgramTemplates.AsNoTracking()
                    .FirstOrDefaultAsync(t => t.WorkoutProgramTemplateID == templateId && t.CompanyID == companyId && t.IsActive == true, ct);
                if (template == null)
                {
                    return new ErrorDataResult<WorkoutProgramTemplate>("Antrenman programı şablonu bulunamadı");
                }
                return new SuccessDataResult<WorkoutProgramTemplate>(template);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<WorkoutProgramTemplate>($"Antrenman programı şablonu getirilirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> AddWorkoutProgramWithBusinessRulesAsync(WorkoutProgramTemplateAddDto templateAddDto, int companyId, CancellationToken ct = default)
        {
            try
            {
                var validationResult = ValidateWorkoutProgramBusinessRules(templateAddDto.ProgramName, templateAddDto.Days, null);
                if (!validationResult.Success)
                {
                    return validationResult;
                }
                await AddWorkoutProgramWithDaysAndExercisesAsync(templateAddDto, companyId, ct);
                return new SuccessResult("Antrenman programı başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Antrenman programı eklenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> UpdateWorkoutProgramWithBusinessRulesAsync(WorkoutProgramTemplateUpdateDto templateUpdateDto, int companyId, CancellationToken ct = default)
        {
            try
            {
                var existingTemplate = await GetAsync(t => t.WorkoutProgramTemplateID == templateUpdateDto.WorkoutProgramTemplateID && t.CompanyID == companyId, ct);
                if (existingTemplate == null)
                {
                    return new ErrorResult("Antrenman programı bulunamadı.");
                }
                var validationResult = ValidateWorkoutProgramBusinessRules(templateUpdateDto.ProgramName, templateUpdateDto.Days, templateUpdateDto.WorkoutProgramTemplateID);
                if (!validationResult.Success)
                {
                    return validationResult;
                }
                await UpdateWorkoutProgramWithDaysAndExercisesAsync(templateUpdateDto, companyId, ct);
                return new SuccessResult("Antrenman programı başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Antrenman programı güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> SoftDeleteWorkoutProgramWithValidationAsync(int templateId, int companyId, CancellationToken ct = default)
        {
            try
            {
                var template = await GetAsync(t => t.WorkoutProgramTemplateID == templateId && t.CompanyID == companyId, ct);
                if (template == null)
                {
                    return new ErrorResult("Antrenman programı bulunamadı.");
                }
                await DeleteAsync(templateId, ct);
                return new SuccessResult("Antrenman programı başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Antrenman programı silinirken hata oluştu: {ex.Message}");
            }
        }

    }
}
