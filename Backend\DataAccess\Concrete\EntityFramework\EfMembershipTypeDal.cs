﻿﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMembershipTypeDal : EfCompanyEntityRepositoryBase<MembershipType, GymContext>, IMembershiptypeDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfMembershipTypeDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }


        // ASYNC versiyonlar
        public async Task<List<PackageWithCountDto>> GetPackagesByBranchAsync(string branch, CancellationToken ct = default)
        {
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;

                // N+1 FIX: MemberCount için subquery hazırla
                var memberCountSubquery = from m in _context.Memberships.AsNoTracking()
                                         where m.IsActive == true
                                               && m.EndDate > now
                                               && m.CompanyID == companyId
                                         group m by m.MembershipTypeID into g
                                         select new { MembershipTypeID = g.Key, MemberCount = g.Count() };

                var packagesWithCount = from mt in _context.MembershipTypes.AsNoTracking()
                                       // N+1 FIX: Subquery'yi join et
                                       join memberCount in memberCountSubquery on mt.MembershipTypeID equals memberCount.MembershipTypeID into memberGroup
                                       from memberCount in memberGroup.DefaultIfEmpty()
                                       where mt.Branch == branch
                                       && mt.IsActive == true
                                       && mt.CompanyID == companyId
                                       select new PackageWithCountDto
                                       {
                                           MembershipTypeID = mt.MembershipTypeID,
                                           Branch = mt.Branch,
                                           TypeName = mt.TypeName,
                                           Day = mt.Day,
                                           Price = mt.Price,
                                           // N+1 FIX: Artık tek sorguda geliyor!
                                           MemberCount = memberCount != null ? memberCount.MemberCount : 0
                                       };

                var list = await packagesWithCount.Where(p => p.MemberCount > 0).ToListAsync(ct);
                return list;
            }

            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }


        public async Task<PaginatedResult<MembershipType>> GetAllPaginatedAsync(MembershipTypePagingParameters parameters, CancellationToken ct = default)
        {
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();

                var query = _context.MembershipTypes
                    .AsNoTracking()
                    .Where(mt => mt.IsActive == true && mt.CompanyID == companyId);

                if (!string.IsNullOrEmpty(parameters.SearchText))
                {
                    var searchText = parameters.SearchText.ToLower();
                    query = query.Where(mt =>
                        mt.Branch.ToLower().Contains(searchText) ||
                        mt.TypeName.ToLower().Contains(searchText));
                }

                if (!string.IsNullOrEmpty(parameters.Branch))
                {
                    query = query.Where(mt => mt.Branch == parameters.Branch);
                }

                if (parameters.MinPrice.HasValue)
                {
                    query = query.Where(mt => mt.Price >= parameters.MinPrice.Value);
                }
                if (parameters.MaxPrice.HasValue)
                {
                    query = query.Where(mt => mt.Price <= parameters.MaxPrice.Value);
                }

                if (parameters.MinDuration.HasValue)
                {
                    query = query.Where(mt => mt.Day >= parameters.MinDuration.Value);
                }
                if (parameters.MaxDuration.HasValue)
                {
                    query = query.Where(mt => mt.Day <= parameters.MaxDuration.Value);
                }

                var orderedQuery = query.OrderByDescending(mt => mt.MembershipTypeID);
                var totalCount = await orderedQuery.CountAsync(ct);
                var items = await orderedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync(ct);

                return new PaginatedResult<MembershipType>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }

            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }


        public async Task<List<BranchGetAllDto>> GetBranchesAndTypesAsync(CancellationToken ct = default)
        {
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();

                var result = await _context.MembershipTypes
                    .AsNoTracking()
                    .Where(mt => mt.IsActive == true && mt.CompanyID == companyId)
                    .Select(mt => new BranchGetAllDto
                    {
                        MembershipTypeID = mt.MembershipTypeID,
                        Branch = mt.Branch,
                        TypeName = mt.TypeName
                    })
                    .ToListAsync(ct);

                return result;
            }

            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }


        public async Task<IResult> SoftDeleteMembershipTypeAsync(int membershipTypeId, int companyId, CancellationToken ct = default)
        {
            try
            {
                if (_context != null)
                {
                    var membershipType = await _context.MembershipTypes.FirstOrDefaultAsync(mt =>
                        mt.MembershipTypeID == membershipTypeId && mt.CompanyID == companyId, ct);

                    if (membershipType == null)
                    {
                        return new ErrorResult("Üyelik türü bulunamadı veya erişim yetkiniz yok.");
                    }

                    membershipType.IsActive = false;
                    membershipType.DeletedDate = DateTime.Now;
                    await _context.SaveChangesAsync(ct);

                    return new SuccessResult("Üyelik türü başarıyla silindi.");
                }

                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üyelik türü silinirken hata oluştu: {ex.Message}");
            }
        }


        public async Task<IResult> UpdateMembershipTypeWithBusinessLogicAsync(MembershipType membershipType, int companyId, CancellationToken ct = default)
        {
            try
            {
                if (_context != null)
                {
                    var existingMembershipType = await _context.MembershipTypes.FirstOrDefaultAsync(mt =>
                        mt.MembershipTypeID == membershipType.MembershipTypeID && mt.CompanyID == companyId, ct);

                    if (existingMembershipType == null)
                    {
                        return new ErrorResult("Üyelik türü bulunamadı veya erişim yetkiniz yok.");
                    }

                    existingMembershipType.TypeName = membershipType.TypeName;
                    existingMembershipType.Day = membershipType.Day;
                    existingMembershipType.Price = membershipType.Price;
                    existingMembershipType.Branch = membershipType.Branch;
                    existingMembershipType.IsActive = membershipType.IsActive;
                    existingMembershipType.UpdatedDate = DateTime.Now;

                    await _context.SaveChangesAsync(ct);

                    return new SuccessResult("Üyelik türü başarıyla güncellendi.");
                }

                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üyelik türü güncellenirken hata oluştu: {ex.Message}");
            }
        }
    }
}
