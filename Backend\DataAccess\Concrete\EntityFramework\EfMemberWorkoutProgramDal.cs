using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;


namespace DataAccess.Concrete.EntityFramework
{
    public class EfMemberWorkoutProgramDal : EfCompanyEntityRepositoryBase<MemberWorkoutProgram, GymContext>, IMemberWorkoutProgramDal
    {
        private readonly ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfMemberWorkoutProgramDal(ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }




        public async Task<List<MemberWorkoutProgramListDto>> GetCompanyAssignmentsAsync(CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            if (companyId <= 0) return new List<MemberWorkoutProgramListDto>();

            // N+1 FIX: DayCount ve ExerciseCount için subquery'ler hazırla
            var dayCountSubquery = from wpd in _context.WorkoutProgramDays.AsNoTracking()
                                   group wpd by wpd.WorkoutProgramTemplateID into g
                                   select new { WorkoutProgramTemplateID = g.Key, DayCount = g.Count() };

            var exerciseCountSubquery = from wpd in _context.WorkoutProgramDays.AsNoTracking()
                                        join wpe in _context.WorkoutProgramExercises.AsNoTracking() on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                        group wpe by wpd.WorkoutProgramTemplateID into g
                                        select new { WorkoutProgramTemplateID = g.Key, ExerciseCount = g.Count() };

            var query = from mwp in _context.MemberWorkoutPrograms.AsNoTracking()
                        join m in _context.Members.AsNoTracking() on mwp.MemberID equals m.MemberID
                        join wpt in _context.WorkoutProgramTemplates.AsNoTracking() on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                        // N+1 FIX: Subquery'leri join et
                        join dayCount in dayCountSubquery on wpt.WorkoutProgramTemplateID equals dayCount.WorkoutProgramTemplateID into dayGroup
                        from dayCount in dayGroup.DefaultIfEmpty()
                        join exerciseCount in exerciseCountSubquery on wpt.WorkoutProgramTemplateID equals exerciseCount.WorkoutProgramTemplateID into exerciseGroup
                        from exerciseCount in exerciseGroup.DefaultIfEmpty()
                        where mwp.CompanyID == companyId && mwp.IsActive == true
                              && m.IsActive == true
                              && m.CompanyID == companyId
                              && wpt.CompanyID == companyId
                        orderby mwp.CreationDate descending
                        select new MemberWorkoutProgramListDto
                        {
                            MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                            MemberID = mwp.MemberID,
                            MemberName = m.Name,
                            ProgramName = wpt.ProgramName,
                            ExperienceLevel = wpt.ExperienceLevel,
                            TargetGoal = wpt.TargetGoal,
                            StartDate = mwp.StartDate,
                            EndDate = mwp.EndDate,
                            IsActive = mwp.IsActive == true,
                            // N+1 FIX: Artık tek sorguda geliyor!
                            DayCount = dayCount != null ? dayCount.DayCount : 0,
                            ExerciseCount = exerciseCount != null ? exerciseCount.ExerciseCount : 0
                        };
            return await query.ToListAsync(ct);
        }



        public async Task<List<MemberWorkoutProgramDto>> GetMemberActiveProgramsAsync(int memberId, CancellationToken ct = default)
        {
            // N+1 FIX: DayCount ve ExerciseCount için subquery'ler hazırla
            var dayCountSubquery = from wpd in _context.WorkoutProgramDays.AsNoTracking()
                                   group wpd by wpd.WorkoutProgramTemplateID into g
                                   select new { WorkoutProgramTemplateID = g.Key, DayCount = g.Count() };

            var exerciseCountSubquery = from wpd in _context.WorkoutProgramDays.AsNoTracking()
                                        join wpe in _context.WorkoutProgramExercises.AsNoTracking() on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                        group wpe by wpd.WorkoutProgramTemplateID into g
                                        select new { WorkoutProgramTemplateID = g.Key, ExerciseCount = g.Count() };

            var query = from mwp in _context.MemberWorkoutPrograms.AsNoTracking()
                        join m in _context.Members.AsNoTracking() on mwp.MemberID equals m.MemberID
                        join wpt in _context.WorkoutProgramTemplates.AsNoTracking() on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                        // N+1 FIX: Subquery'leri join et
                        join dayCount in dayCountSubquery on wpt.WorkoutProgramTemplateID equals dayCount.WorkoutProgramTemplateID into dayGroup
                        from dayCount in dayGroup.DefaultIfEmpty()
                        join exerciseCount in exerciseCountSubquery on wpt.WorkoutProgramTemplateID equals exerciseCount.WorkoutProgramTemplateID into exerciseGroup
                        from exerciseCount in exerciseGroup.DefaultIfEmpty()
                        where mwp.MemberID == memberId && mwp.IsActive == true && m.IsActive == true
                        orderby mwp.StartDate descending
                        select new MemberWorkoutProgramDto
                        {
                            MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                            MemberID = mwp.MemberID,
                            MemberName = m.Name,
                            MemberPhone = m.PhoneNumber,
                            WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                            ProgramName = wpt.ProgramName,
                            ProgramDescription = wpt.Description,
                            ExperienceLevel = wpt.ExperienceLevel,
                            TargetGoal = wpt.TargetGoal,
                            CompanyID = mwp.CompanyID,
                            StartDate = mwp.StartDate,
                            EndDate = mwp.EndDate,
                            Notes = mwp.Notes,
                            IsActive = mwp.IsActive == true,
                            CreationDate = mwp.CreationDate,
                            // N+1 FIX: Artık tek sorguda geliyor!
                            DayCount = dayCount != null ? dayCount.DayCount : 0,
                            ExerciseCount = exerciseCount != null ? exerciseCount.ExerciseCount : 0
                        };
            return await query.ToListAsync(ct);
        }

        public async Task<List<MemberWorkoutProgramHistoryDto>> GetMemberProgramHistoryAsync(int memberId, CancellationToken ct = default)
        {
            var query = from mwp in _context.MemberWorkoutPrograms.AsNoTracking()
                        join m in _context.Members.AsNoTracking() on mwp.MemberID equals m.MemberID
                        join wpt in _context.WorkoutProgramTemplates.AsNoTracking() on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                        where mwp.MemberID == memberId && m.IsActive == true
                        orderby mwp.CreationDate descending
                        select new MemberWorkoutProgramHistoryDto
                        {
                            MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                            ProgramName = wpt.ProgramName,
                            StartDate = mwp.StartDate,
                            EndDate = mwp.EndDate,
                            IsActive = mwp.IsActive == true,
                            Notes = mwp.Notes,
                            CreationDate = mwp.CreationDate
                        };
            return await query.ToListAsync(ct);
        }


        public async Task<List<MemberActiveWorkoutProgramDto>> GetActiveWorkoutProgramsByUserIdAsync(int userId, CancellationToken ct = default)
        {
            // N+1 FIX: DayCount ve ExerciseCount için subquery'ler hazırla
            var dayCountSubquery = from wpd in _context.WorkoutProgramDays.AsNoTracking()
                                   group wpd by wpd.WorkoutProgramTemplateID into g
                                   select new { WorkoutProgramTemplateID = g.Key, DayCount = g.Count() };

            var exerciseCountSubquery = from wpd in _context.WorkoutProgramDays.AsNoTracking()
                                        join wpe in _context.WorkoutProgramExercises.AsNoTracking() on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                        group wpe by wpd.WorkoutProgramTemplateID into g
                                        select new { WorkoutProgramTemplateID = g.Key, ExerciseCount = g.Count() };

            var query = from mwp in _context.MemberWorkoutPrograms.AsNoTracking()
                        join m in _context.Members.AsNoTracking() on mwp.MemberID equals m.MemberID
                        join wpt in _context.WorkoutProgramTemplates.AsNoTracking() on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                        // N+1 FIX: Subquery'leri join et
                        join dayCount in dayCountSubquery on wpt.WorkoutProgramTemplateID equals dayCount.WorkoutProgramTemplateID into dayGroup
                        from dayCount in dayGroup.DefaultIfEmpty()
                        join exerciseCount in exerciseCountSubquery on wpt.WorkoutProgramTemplateID equals exerciseCount.WorkoutProgramTemplateID into exerciseGroup
                        from exerciseCount in exerciseGroup.DefaultIfEmpty()
                        where m.UserID == userId && mwp.IsActive == true && m.IsActive == true
                              && mwp.CompanyID == m.CompanyID && wpt.CompanyID == m.CompanyID
                        orderby mwp.StartDate descending
                        select new MemberActiveWorkoutProgramDto
                        {
                            MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                            WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                            ProgramName = wpt.ProgramName,
                            ProgramDescription = wpt.Description,
                            ExperienceLevel = wpt.ExperienceLevel,
                            TargetGoal = wpt.TargetGoal,
                            StartDate = mwp.StartDate,
                            EndDate = mwp.EndDate,
                            Notes = mwp.Notes,
                            // N+1 FIX: Artık tek sorguda geliyor!
                            DayCount = dayCount != null ? dayCount.DayCount : 0,
                            ExerciseCount = exerciseCount != null ? exerciseCount.ExerciseCount : 0
                        };
            return await query.ToListAsync(ct);
        }

        public async Task<MemberWorkoutProgramDto> GetAssignmentDetailAsync(int assignmentId, CancellationToken ct = default)
        {
            var query = from mwp in _context.MemberWorkoutPrograms.AsNoTracking()
                        join m in _context.Members.AsNoTracking() on mwp.MemberID equals m.MemberID
                        join wpt in _context.WorkoutProgramTemplates.AsNoTracking() on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                        where mwp.MemberWorkoutProgramID == assignmentId
                        select new MemberWorkoutProgramDto
                        {
                            MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                            MemberID = mwp.MemberID,
                            MemberName = m.Name,
                            MemberPhone = m.PhoneNumber,
                            WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                            ProgramName = wpt.ProgramName,
                            ProgramDescription = wpt.Description,
                            ExperienceLevel = wpt.ExperienceLevel,
                            TargetGoal = wpt.TargetGoal,
                            CompanyID = mwp.CompanyID,
                            StartDate = mwp.StartDate,
                            EndDate = mwp.EndDate,
                            Notes = mwp.Notes,
                            IsActive = mwp.IsActive == true,
                            CreationDate = mwp.CreationDate
                        };
            return await query.FirstOrDefaultAsync(ct);
        }




        public async Task<int> GetAssignedMemberCountAsync(int workoutProgramTemplateId, CancellationToken ct = default)
        {
            return await _context.MemberWorkoutPrograms
                .AsNoTracking()
                .Where(mwp => mwp.WorkoutProgramTemplateID == workoutProgramTemplateId && mwp.IsActive == true)
                .CountAsync(ct);
        }

        public async Task<int> GetActiveAssignmentCountAsync(int companyId, CancellationToken ct = default)
        {
            return await _context.MemberWorkoutPrograms
                .AsNoTracking()
                .Where(mwp => mwp.CompanyID == companyId && mwp.IsActive == true)
                .CountAsync(ct);
        }

        public async Task<MemberWorkoutProgramDetailDto> GetProgramDetailByUserAsync(int userId, int memberWorkoutProgramId, CancellationToken ct = default)
        {
            // N+1 FIX: DayCount ve ExerciseCount için subquery'ler hazırla
            var dayCountSubquery = from wpd in _context.WorkoutProgramDays.AsNoTracking()
                                   group wpd by wpd.WorkoutProgramTemplateID into g
                                   select new { WorkoutProgramTemplateID = g.Key, DayCount = g.Count() };

            var exerciseCountSubquery = from wpd in _context.WorkoutProgramDays.AsNoTracking()
                                        join wpe in _context.WorkoutProgramExercises.AsNoTracking() on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                        group wpe by wpd.WorkoutProgramTemplateID into g
                                        select new { WorkoutProgramTemplateID = g.Key, ExerciseCount = g.Count() };

            var assignment = await (from mwp in _context.MemberWorkoutPrograms.AsNoTracking()
                                    join m in _context.Members.AsNoTracking() on mwp.MemberID equals m.MemberID
                                    join wpt in _context.WorkoutProgramTemplates.AsNoTracking() on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                                    // N+1 FIX: Subquery'leri join et
                                    join dayCount in dayCountSubquery on wpt.WorkoutProgramTemplateID equals dayCount.WorkoutProgramTemplateID into dayGroup
                                    from dayCount in dayGroup.DefaultIfEmpty()
                                    join exerciseCount in exerciseCountSubquery on wpt.WorkoutProgramTemplateID equals exerciseCount.WorkoutProgramTemplateID into exerciseGroup
                                    from exerciseCount in exerciseGroup.DefaultIfEmpty()
                                    where m.UserID == userId && mwp.MemberWorkoutProgramID == memberWorkoutProgramId && mwp.IsActive == true && m.IsActive == true
                                          && mwp.CompanyID == m.CompanyID && wpt.CompanyID == m.CompanyID
                                    select new MemberWorkoutProgramDetailDto
                                    {
                                        MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                        MemberID = mwp.MemberID,
                                        MemberName = m.Name,
                                        WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                                        ProgramName = wpt.ProgramName,
                                        ProgramDescription = wpt.Description,
                                        ExperienceLevel = wpt.ExperienceLevel,
                                        TargetGoal = wpt.TargetGoal,
                                        StartDate = mwp.StartDate,
                                        EndDate = mwp.EndDate,
                                        Notes = mwp.Notes,
                                        IsActive = mwp.IsActive == true,
                                        // N+1 FIX: Artık tek sorguda geliyor!
                                        DayCount = dayCount != null ? dayCount.DayCount : 0,
                                        ExerciseCount = exerciseCount != null ? exerciseCount.ExerciseCount : 0
                                    }).FirstOrDefaultAsync(ct);

            if (assignment != null)
            {
                assignment.Days = await GetWorkoutProgramDaysWithExercisesAsync(_context, assignment.WorkoutProgramTemplateID, ct);
            }

            return assignment;
        }







        public async Task<IResult> AssignProgramWithValidationAsync(MemberWorkoutProgramAddDto assignmentDto, int companyId, CancellationToken ct = default)
        {
            try
            {
                if (!CheckIfMemberExists(assignmentDto.MemberID))
                    return new ErrorResult("Üye bulunamadı");
                if (!CheckIfProgramExists(assignmentDto.WorkoutProgramTemplateID))
                    return new ErrorResult("Program şablonu bulunamadı");
                if (!CheckIfMemberBelongsToCompany(assignmentDto.MemberID, companyId))
                    return new ErrorResult("Üye bu şirkete ait değil");
                if (!CheckIfProgramBelongsToCompany(assignmentDto.WorkoutProgramTemplateID, companyId))
                    return new ErrorResult("Program şablonu bu şirkete ait değil");
                if (!CheckDateRange(assignmentDto.StartDate, assignmentDto.EndDate))
                    return new ErrorResult("Geçersiz tarih aralığı");
                if (CheckIfMemberHasActiveProgramAssignment(assignmentDto.MemberID, assignmentDto.WorkoutProgramTemplateID))
                    return new ErrorResult("Bu üyeye bu program zaten atanmış");

                var assignment = new MemberWorkoutProgram
                {
                    MemberID = assignmentDto.MemberID,
                    WorkoutProgramTemplateID = assignmentDto.WorkoutProgramTemplateID,
                    CompanyID = companyId,
                    StartDate = assignmentDto.StartDate,
                    EndDate = assignmentDto.EndDate,
                    Notes = assignmentDto.Notes,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                await _context.MemberWorkoutPrograms.AddAsync(assignment, ct);
                await _context.SaveChangesAsync(ct);
                return new SuccessResult("Program başarıyla atandı");
            }
            catch (DbUpdateException ex) when (ex.InnerException?.Message?.Contains("duplicate") == true ||
                                               ex.InnerException?.Message?.Contains("UNIQUE") == true ||
                                               ex.InnerException?.Message?.Contains("PRIMARY KEY") == true)
            {
                return new ErrorResult("Bu üyeye bu program zaten atanmış");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Program atanırken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> UpdateAssignmentWithValidationAsync(MemberWorkoutProgramUpdateDto assignmentDto, int companyId, CancellationToken ct = default)
        {
            try
            {
                var existingAssignment = await _context.MemberWorkoutPrograms
                    .FirstOrDefaultAsync(a => a.MemberWorkoutProgramID == assignmentDto.MemberWorkoutProgramID && a.CompanyID == companyId, ct);
                if (existingAssignment == null)
                {
                    return new ErrorResult("Program ataması bulunamadı.");
                }
                if (!CheckIfProgramExists(assignmentDto.WorkoutProgramTemplateID))
                    return new ErrorResult("Program şablonu bulunamadı");
                if (!CheckIfProgramBelongsToCompany(assignmentDto.WorkoutProgramTemplateID, companyId))
                    return new ErrorResult("Program şablonu bu şirkete ait değil");
                if (!CheckDateRange(assignmentDto.StartDate, assignmentDto.EndDate))
                    return new ErrorResult("Geçersiz tarih aralığı");

                existingAssignment.WorkoutProgramTemplateID = assignmentDto.WorkoutProgramTemplateID;
                existingAssignment.StartDate = assignmentDto.StartDate;
                existingAssignment.EndDate = assignmentDto.EndDate;
                existingAssignment.Notes = assignmentDto.Notes;
                existingAssignment.IsActive = assignmentDto.IsActive;
                existingAssignment.UpdatedDate = DateTime.Now;

                await _context.SaveChangesAsync(ct);
                return new SuccessResult("Program ataması başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Program ataması güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> DeleteAssignmentWithValidationAsync(int assignmentId, int companyId, CancellationToken ct = default)
        {
            try
            {
                var assignment = await _context.MemberWorkoutPrograms
                    .FirstOrDefaultAsync(a => a.MemberWorkoutProgramID == assignmentId && a.CompanyID == companyId, ct);
                if (assignment == null)
                {
                    return new ErrorResult("Program ataması bulunamadı.");
                }

                assignment.IsActive = false;
                assignment.DeletedDate = DateTime.Now;

                await _context.SaveChangesAsync(ct);
                return new SuccessResult("Program ataması başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Program ataması silinirken hata oluştu: {ex.Message}");
            }
        }


        /// <summary>
        /// SOLID prensiplerine uygun: Complex business logic DAL katmanında
        /// </summary>


        // Helper validation methods
        private bool CheckIfMemberExists(int memberId)
        {
            // DI kullanılıyor - Scalability optimized
            return _context.Members.Any(m => m.MemberID == memberId && m.IsActive == true);
        }

        private bool CheckIfProgramExists(int programId)
        {
            // DI kullanılıyor - Scalability optimized
            return _context.WorkoutProgramTemplates.Any(p => p.WorkoutProgramTemplateID == programId && p.IsActive == true);
        }

        private bool CheckIfMemberBelongsToCompany(int memberId, int companyId)
        {
            // DI kullanılıyor - Scalability optimized
            return _context.Members.Any(m => m.MemberID == memberId && m.CompanyID == companyId);
        }

        private bool CheckIfProgramBelongsToCompany(int programId, int companyId)
        {
            // DI kullanılıyor - Scalability optimized
            return _context.WorkoutProgramTemplates.Any(p => p.WorkoutProgramTemplateID == programId && p.CompanyID == companyId);
        }

        private bool CheckIfMemberHasActiveProgramAssignment(int memberId, int programId)
        {
            // DI kullanılıyor - Scalability optimized
            return _context.MemberWorkoutPrograms.Any(mwp => mwp.MemberID == memberId && mwp.WorkoutProgramTemplateID == programId && mwp.IsActive == true);
        }

        private bool CheckDateRange(DateTime? startDate, DateTime? endDate)
        {
            if (!startDate.HasValue)
                return false;

            // EndDate opsiyonel - eğer yoksa sadece startDate kontrolü yap
            if (!endDate.HasValue)
                return true; // StartDate varsa yeterli

            // Her iki tarih de varsa karşılaştır
            return startDate.Value < endDate.Value;
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Update business logic DAL katmanında
        /// </summary>


        /// <summary>

        private async Task<List<WorkoutProgramDayDto>> GetWorkoutProgramDaysWithExercisesAsync(GymContext context, int workoutProgramTemplateId, CancellationToken ct)
        {
            var days = await (from wpd in context.WorkoutProgramDays.AsNoTracking()
                               where wpd.WorkoutProgramTemplateID == workoutProgramTemplateId
                               orderby wpd.DayNumber
                               select new WorkoutProgramDayDto
                               {
                                   WorkoutProgramDayID = wpd.WorkoutProgramDayID,
                                   WorkoutProgramTemplateID = wpd.WorkoutProgramTemplateID,
                                   DayNumber = wpd.DayNumber,
                                   DayName = wpd.DayName,
                                   IsRestDay = wpd.IsRestDay,
                                   CreationDate = wpd.CreationDate
                               }).ToListAsync(ct);

            if (days.Count == 0)
            {
                return days;
            }

            var dayIds = days.Select(d => d.WorkoutProgramDayID).ToList();

            var exercises = await (from wpe in context.WorkoutProgramExercises.AsNoTracking()
                                   where dayIds.Contains(wpe.WorkoutProgramDayID)
                                   orderby wpe.WorkoutProgramDayID, wpe.OrderIndex
                                   select new WorkoutProgramExerciseDto
                                   {
                                       WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                                       WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                                       ExerciseType = wpe.ExerciseType,
                                       ExerciseID = wpe.ExerciseID,
                                       OrderIndex = wpe.OrderIndex,
                                       Sets = wpe.Sets,
                                       Reps = wpe.Reps,
                                       RestTime = wpe.RestTime,
                                       Notes = wpe.Notes,
                                       CreationDate = wpe.CreationDate
                                   }).ToListAsync(ct);

            // Egzersiz ad/descr/kategori bilgisini batch olarak çek
            var systemIds = exercises.Where(e => e.ExerciseType == "System").Select(e => e.ExerciseID).Distinct().ToList();
            var companyIds = exercises.Where(e => e.ExerciseType == "Company").Select(e => e.ExerciseID).Distinct().ToList();

            var systemMeta = systemIds.Count > 0
                ? await (from e in context.SystemExercises.AsNoTracking()
                         join ec in context.ExerciseCategories.AsNoTracking() on e.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where systemIds.Contains(e.SystemExerciseID)
                         select new { e.SystemExerciseID, e.ExerciseName, e.Description, ec.CategoryName })
                        .ToDictionaryAsync(x => x.SystemExerciseID, x => (x.ExerciseName, x.Description, x.CategoryName), ct)
                : new Dictionary<int, (string ExerciseName, string Description, string CategoryName)>();

            var companyMeta = companyIds.Count > 0
                ? await (from ce in context.CompanyExercises.AsNoTracking()
                         join ec in context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where companyIds.Contains(ce.CompanyExerciseID)
                         select new { ce.CompanyExerciseID, ce.ExerciseName, ce.Description, ec.CategoryName })
                        .ToDictionaryAsync(x => x.CompanyExerciseID, x => (x.ExerciseName, x.Description, x.CategoryName), ct)
                : new Dictionary<int, (string ExerciseName, string Description, string CategoryName)>();

            foreach (var ex in exercises)
            {
                if (ex.ExerciseType == "System" && systemMeta.TryGetValue(ex.ExerciseID, out var sm))
                {
                    ex.ExerciseName = sm.ExerciseName;
                    ex.ExerciseDescription = sm.Description;
                    ex.CategoryName = sm.CategoryName;
                }
                else if (ex.ExerciseType == "Company" && companyMeta.TryGetValue(ex.ExerciseID, out var cm))
                {
                    ex.ExerciseName = cm.ExerciseName;
                    ex.ExerciseDescription = cm.Description;
                    ex.CategoryName = cm.CategoryName;
                }
            }

            var exercisesByDay = exercises
                .GroupBy(e => e.WorkoutProgramDayID)
                .ToDictionary(g => g.Key, g => g.OrderBy(x => x.OrderIndex).ToList());

            foreach (var day in days)
            {
                if (exercisesByDay.TryGetValue(day.WorkoutProgramDayID, out var list))
                {
                    day.Exercises = list;
                }
                else
                {
                    day.Exercises = new List<WorkoutProgramExerciseDto>();
                }
            }

            return days;
        }

        private async Task<List<WorkoutProgramExerciseDto>> GetWorkoutProgramExercisesAsync(GymContext context, int workoutProgramDayId, CancellationToken ct)
        {
            var exercises = await (from wpe in context.WorkoutProgramExercises.AsNoTracking()
                                   where wpe.WorkoutProgramDayID == workoutProgramDayId
                                   orderby wpe.OrderIndex
                                   select new WorkoutProgramExerciseDto
                                   {
                                       WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                                       WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                                       ExerciseType = wpe.ExerciseType,
                                       ExerciseID = wpe.ExerciseID,
                                       OrderIndex = wpe.OrderIndex,
                                       Sets = wpe.Sets,
                                       Reps = wpe.Reps,
                                       RestTime = wpe.RestTime,
                                       Notes = wpe.Notes,
                                       CreationDate = wpe.CreationDate
                                   }).ToListAsync(ct);

            if (exercises.Count == 0)
            {
                return exercises;
            }

            var systemIds = exercises.Where(e => e.ExerciseType == "System").Select(e => e.ExerciseID).Distinct().ToList();
            var companyIds = exercises.Where(e => e.ExerciseType == "Company").Select(e => e.ExerciseID).Distinct().ToList();

            var systemMeta = systemIds.Count > 0
                ? await (from e in context.SystemExercises.AsNoTracking()
                         join ec in context.ExerciseCategories.AsNoTracking() on e.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where systemIds.Contains(e.SystemExerciseID)
                         select new { e.SystemExerciseID, e.ExerciseName, e.Description, ec.CategoryName })
                        .ToDictionaryAsync(x => x.SystemExerciseID, x => (x.ExerciseName, x.Description, x.CategoryName), ct)
                : new Dictionary<int, (string ExerciseName, string Description, string CategoryName)>();

            var companyMeta = companyIds.Count > 0
                ? await (from ce in context.CompanyExercises.AsNoTracking()
                         join ec in context.ExerciseCategories.AsNoTracking() on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                         where companyIds.Contains(ce.CompanyExerciseID)
                         select new { ce.CompanyExerciseID, ce.ExerciseName, ce.Description, ec.CategoryName })
                        .ToDictionaryAsync(x => x.CompanyExerciseID, x => (x.ExerciseName, x.Description, x.CategoryName), ct)
                : new Dictionary<int, (string ExerciseName, string Description, string CategoryName)>();

            foreach (var ex in exercises)
            {
                if (ex.ExerciseType == "System" && systemMeta.TryGetValue(ex.ExerciseID, out var sm))
                {
                    ex.ExerciseName = sm.ExerciseName;
                    ex.ExerciseDescription = sm.Description;
                    ex.CategoryName = sm.CategoryName;
                }
                else if (ex.ExerciseType == "Company" && companyMeta.TryGetValue(ex.ExerciseID, out var cm))
                {
                    ex.ExerciseName = cm.ExerciseName;
                    ex.ExerciseDescription = cm.Description;
                    ex.CategoryName = cm.CategoryName;
                }
            }

            return exercises;
        }

        /// SOLID prensiplerine uygun: Delete business logic DAL katmanında
        /// </summary>

    }
}
